comment: "Please only create issues with the provided [issue creator](https://issues.vuetifyjs.com). In the boilerplate for creating an issue, it explains that any ticket made without this will be automatically closed. For general questions, please join [the Discord chat room](https://community.vuetifyjs.com). You can also check [reddit](https://www.reddit.com/r/vuetifyjs/) or [stackoverflow](https://stackoverflow.com/questions/tagged/vuetify.js). Thank you."
issueConfigs:
- content:
  - "<!-- generated by vuetify-issue-helper. DO NOT REMOVE -->"
- content:
  - "<!-- override-close -->"
label: "invalid"
exception:
  - "john<PERSON>ider"
  - "<PERSON>el<PERSON>"
  - "MajesticPotatoe"
