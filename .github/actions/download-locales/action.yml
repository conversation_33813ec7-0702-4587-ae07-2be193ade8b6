name: Download translations
description: Download translations from Crowdin

inputs:
  crowdin-branch:
    description: 'Crowdin branch name'
    required: false
    default: 'v3'

runs:
  using: composite
  steps:
    - name: Download eo-UY
      uses: crowdin/github-action@v2.7.1
      with:
        download_language: eo
        config: crowdin.yml
        upload_sources: false
        download_translations: true
        push_translations: false
        export_only_approved: false
        crowdin_branch_name: ${{ inputs.crowdin-branch }}
    - name: Download es-MX
      uses: crowdin/github-action@v2.7.1
      with:
        download_language: es-MX
        config: crowdin.yml
        upload_sources: false
        download_translations: true
        push_translations: false
        export_only_approved: false
        crowdin_branch_name: ${{ inputs.crowdin-branch }}
    - name: Download ja-JP
      uses: crowdin/github-action@v2.7.1
      with:
        download_language: ja
        config: crowdin.yml
        upload_sources: false
        download_translations: true
        push_translations: false
        export_only_approved: false
        crowdin_branch_name: ${{ inputs.crowdin-branch }}
    - name: Download zh-CN
      uses: crowdin/github-action@v2.7.1
      with:
        download_language: zh-CN
        config: crowdin.yml
        upload_sources: false
        download_translations: true
        push_translations: false
        export_only_approved: false
        crowdin_branch_name: ${{ inputs.crowdin-branch }}
