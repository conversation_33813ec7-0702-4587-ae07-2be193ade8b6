name: Issue triage
on:
  issues:
    types: [opened, labeled, unlabeled, closed]

jobs:
  triage:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: vuetifyjs/triage-action@master
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          triageLabel: 'S: triage'
          staleLabel: 'S: stale'
          sponsorsFile: '.github/sponsors.yml'
          duplicateLabel: 'duplicate'
          triagedLabels: |-
            T: documentation
            T: bug
            T: enhancement
            T: feature
            T: question
            Epic
            Task
            duplicate
            layer 8 issue
            invalid
            wontfix
            working as intended
