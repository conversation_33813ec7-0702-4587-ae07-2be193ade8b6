name: CI
on: [push, pull_request]
concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
  CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  pre_job:
    runs-on: ubuntu-24.04
    outputs:
      should_skip: ${{ steps.skip_check.outputs.should_skip }}
    steps:
      - id: skip_check
        if: ${{ !startswith(github.ref, 'refs/tags/v') && github.ref != 'refs/heads/master' }}
        uses: fkirc/skip-duplicate-actions@master
        with:
          skip_after_successful_duplicate: 'true'
          concurrent_skipping: same_content
          do_not_skip: '["pull_request", "workflow_dispatch", "schedule"]'

  build-vuetify:
    name: Build vuetify
    needs: pre_job
    if: needs.pre_job.outputs.should_skip != 'true'
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: vuetifyjs/setup-action@master
      - run: pnpm build vuetify
      - uses: actions/upload-artifact@v4
        with:
          name: vuetify-dist
          path: |
            packages/vuetify/dist
            packages/vuetify/lib

  lint:
    name: Lint
    needs: [pre_job, build-vuetify]
    if: needs.pre_job.outputs.should_skip != 'true'
    runs-on: ubuntu-24.04
    strategy:
      fail-fast: false
      matrix:
        scopes: ['--scope vuetify --scope @vuetify/api-generator', '--scope vuetifyjs.com']
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: vuetify-dist
          path: packages/vuetify
      - uses: vuetifyjs/setup-action@master
      - run: pnpm lerna run lint $SCOPES
        env:
          SCOPES: ${{ matrix.scopes }}

  test-unit:
    name: Test (Unit)
    needs: pre_job
    if: needs.pre_job.outputs.should_skip != 'true'
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: vuetifyjs/setup-action@master
      - run: pnpm run test --project unit
        working-directory: ./packages/vuetify

  test-e2e:
    name: Test (e2e)
    needs: pre_job
    if: needs.pre_job.outputs.should_skip != 'true'
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: vuetifyjs/setup-action@master
      - run: pnpm run test --project browser
        working-directory: ./packages/vuetify
      - uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: Test screenshots
          path: packages/vuetify/test/__screenshots__
          if-no-files-found: ignore
          overwrite: true

  deploy:
    needs: [lint, test-unit, test-e2e, build-vuetify]
    runs-on: ubuntu-24.04
    if: github.event_name == 'push' && startswith(github.ref, 'refs/tags/v') && github.repository_owner == 'vuetifyjs'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: actions/download-artifact@v4
        with:
          name: vuetify-dist
          path: packages/vuetify
      - uses: vuetifyjs/setup-action@master
      - run: pnpm build api
      - run: echo "RELEASE_TAG=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV
      - name: NPM Release
        run: bash scripts/deploy.sh
        env:
          NPM_API_KEY: ${{ secrets.NPM_TOKEN }}
          RELEASE_TAG: ${{ env.RELEASE_TAG }}
      - name: GitHub release
        id: create_release
        run: pnpm conventional-github-releaser -p vuetify
        env:
          DEBUG: '*'
          CONVENTIONAL_GITHUB_RELEASER_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  build-docs:
    name: Build docs
    needs: [pre_job, build-vuetify]
    if: needs.pre_job.outputs.should_skip != 'true' && github.event_name == 'push' && github.repository_owner == 'vuetifyjs' && (github.ref == 'refs/heads/master' || github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/next')
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: vuetify-dist
          path: packages/vuetify
      - uses: vuetifyjs/setup-action@master
      - uses: ./.github/actions/download-locales
      - run: pnpm build api
      - run: pnpm build docs
        env:
          NODE_OPTIONS: --max-old-space-size=6144
          VITE_COSMIC_2_BUCKET_SLUG: ${{ secrets.COSMIC_2_BUCKET_SLUG }}
          VITE_COSMIC_2_BUCKET_READ_KEY: ${{ secrets.COSMIC_2_BUCKET_READ_KEY }}
          VITE_COSMIC_BUCKET_SLUG: ${{ secrets.COSMIC_BUCKET_SLUG }}
          VITE_COSMIC_BUCKET_READ_KEY: ${{ secrets.COSMIC_BUCKET_READ_KEY }}
          VITE_COSMIC_BUCKET_SLUG_STORE: ${{ secrets.COSMIC_BUCKET_SLUG_STORE }}
          VITE_COSMIC_BUCKET_READ_KEY_STORE: ${{ secrets.COSMIC_BUCKET_READ_KEY_STORE }}
          VITE_EMAILJS_PUBLIC_KEY: ${{ secrets.EMAILJS_PUBLIC_KEY }}
          VITE_EMAILJS_SERVICE_ID: ${{ secrets.EMAILJS_SERVICE_ID }}
          VITE_EMAILJS_TEMPLATE_ID: ${{ secrets.EMAILJS_TEMPLATE_ID }}
          VITE_API_SERVER_URL: ${{ secrets.API_SERVER_URL }}
          VITE_GITHUB_SHA: ${{ github.sha }}
          VITE_GITHUB_REF: ${{ github.ref_name }}
      - uses: actions/upload-artifact@v4
        with:
          name: docs-dist
          path: packages/docs/dist

  publish-docs:
    needs: [lint, test-unit, build-docs]
    runs-on: ubuntu-24.04
    environment: ${{ github.ref_name }}
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          name: docs-dist
          path: packages/docs/dist
      - uses: vuetifyjs/coolify-action@master
        with:
          token: ${{ secrets.GITHUB_TOKEN  }}
          imageName: docs
          coolifyWebhook: ${{ secrets.COOLIFY_WEBHOOK }}
          coolifySecret: ${{ secrets.COOLIFY_TOKEN }}
