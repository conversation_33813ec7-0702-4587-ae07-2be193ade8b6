name: Crowdin Uploads
concurrency:
  group: crowdin-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - 'master'
    paths:
    - 'packages/api-generator/src/locale/en/**'
    - 'packages/docs/src/i18n/messages/en.json'
    - 'packages/docs/src/pages/en/**'
    - '.github/workflows/**'

env:
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
  CROWDIN_PERSONAL_TOKEN: ${{ secrets.CROWDIN_PERSONAL_TOKEN }}
  CROWDIN_BRANCH: v3

jobs:
  upload-to-crowdin:
    runs-on: ubuntu-24.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Upload
        uses: crowdin/github-action@v2.7.1
        with:
          config: crowdin.yml
          crowdin_branch_name: ${{ env.CROWDIN_BRANCH }}
