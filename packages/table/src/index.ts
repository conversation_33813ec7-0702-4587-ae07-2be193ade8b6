import type { App } from 'vue'

// 导入组件
import GTable from './components/GTable/index.vue'
import GExcelExport from './components/GExcelExport/index.vue'

// 导入工具函数
import { exportToExcel } from './utils/excel'
import type { TableColumn, ExportOptions } from './types'

// 组件列表
const components = [
  GTable,
  GExcelExport
]

// 定义 install 方法
const install = (app: App): void => {
  components.forEach(component => {
    const name = component.name || component.__name || 'UnknownComponent'
    app.component(name, component)
  })
}

// 导出组件和工具
export {
  // 组件
  GTable,
  GExcelExport,
  // 工具函数
  exportToExcel,
  // 类型
  type TableColumn,
  type ExportOptions,
  // 安装方法
  install
}

// 默认导出
export default {
  install
}
