import * as XLSX from 'xlsx'
import type { TableData, TableColumn, ExportOptions } from '../types'

/**
 * 导出数据到 Excel 文件
 * @param data 要导出的数据
 * @param columns 列定义
 * @param options 导出选项
 */
export function exportToExcel(
  data: TableData[],
  columns: TableColumn[],
  options: ExportOptions = {}
): void {
  const {
    fileName = 'export.xlsx',
    sheetName = 'Sheet1',
    includeHeaders = true,
    columnKeys,
    processCellCallback,
    processHeaderCallback
  } = options

  // 过滤需要导出的列
  const exportColumns = columnKeys 
    ? columns.filter(col => columnKeys.includes(col.field))
    : columns

  // 准备表头
  const headers = includeHeaders 
    ? exportColumns.map(col => {
        const headerName = processHeaderCallback 
          ? processHeaderCallback({ column: col })
          : (col.headerName || col.field)
        return headerName
      })
    : []

  // 准备数据行
  const rows = data.map(row => {
    return exportColumns.map(col => {
      let value = row[col.field]
      
      // 如果有自定义处理函数，使用它
      if (processCellCallback) {
        value = processCellCallback({ 
          data: row, 
          column: col, 
          value 
        })
      }
      
      // 如果有 valueGetter，使用它
      if (col.valueGetter) {
        value = col.valueGetter({ data: row })
      }
      
      return value
    })
  })

  // 合并表头和数据
  const worksheetData = includeHeaders ? [headers, ...rows] : rows

  // 创建工作表
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)

  // 设置列宽
  const colWidths = exportColumns.map(col => ({
    wch: Math.max(
      (col.headerName || col.field).length,
      col.width ? col.width / 10 : 15
    )
  }))
  worksheet['!cols'] = colWidths

  // 创建工作簿
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName)

  // 导出文件
  XLSX.writeFile(workbook, fileName)
}

/**
 * 从文件读取 Excel 数据
 * @param file 文件对象
 * @param sheetName 工作表名称，默认读取第一个
 * @returns Promise<TableData[]>
 */
export function readExcelFile(
  file: File,
  sheetName?: string
): Promise<TableData[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer)
        const workbook = XLSX.read(data, { type: 'array' })
        
        const targetSheetName = sheetName || workbook.SheetNames[0]
        const worksheet = workbook.Sheets[targetSheetName]
        
        if (!worksheet) {
          reject(new Error(`Sheet "${targetSheetName}" not found`))
          return
        }
        
        const jsonData = XLSX.utils.sheet_to_json(worksheet)
        resolve(jsonData as TableData[])
      } catch (error) {
        reject(error)
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }
    
    reader.readAsArrayBuffer(file)
  })
}
