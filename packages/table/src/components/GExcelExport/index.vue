<template>
  <div class="g-excel-export">
    <button
      :class="buttonClass"
      :disabled="disabled || loading"
      @click="handleExport"
    >
      <span v-if="loading" class="loading-icon">⏳</span>
      <span v-else class="export-icon">📊</span>
      {{ loading ? loadingText : buttonText }}
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { exportToExcel } from '../../utils/excel'
import type { ExcelExportProps, ExportOptions } from '../../types'

defineOptions({
  name: 'GExcelExport'
})

interface Props extends ExcelExportProps {
  buttonText?: string
  loadingText?: string
  buttonClass?: string
  disabled?: boolean
  exportOptions?: Partial<ExportOptions>
}

const props = withDefaults(defineProps<Props>(), {
  fileName: 'export.xlsx',
  sheetName: 'Sheet1',
  autoWidth: true,
  buttonText: '导出 Excel',
  loadingText: '导出中...',
  buttonClass: 'g-export-button',
  disabled: false,
  exportOptions: () => ({})
})

const emit = defineEmits<{
  beforeExport: []
  afterExport: [success: boolean, error?: Error]
}>()

const loading = ref(false)

const handleExport = async () => {
  if (loading.value || props.disabled) return

  try {
    loading.value = true
    emit('beforeExport')

    const options: ExportOptions = {
      fileName: props.fileName,
      sheetName: props.sheetName,
      includeHeaders: true,
      ...props.exportOptions
    }

    await new Promise(resolve => setTimeout(resolve, 100)) // 短暂延迟以显示加载状态

    exportToExcel(props.data, props.columns, options)

    emit('afterExport', true)
  } catch (error) {
    console.error('Export failed:', error)
    emit('afterExport', false, error as Error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  export: handleExport
})
</script>

<style scoped>
.g-excel-export {
  display: inline-block;
}

.g-export-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s ease;
}

.g-export-button:hover:not(:disabled) {
  background-color: #337ecc;
}

.g-export-button:disabled {
  background-color: #c0c4cc;
  cursor: not-allowed;
}

.loading-icon,
.export-icon {
  font-size: 16px;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
