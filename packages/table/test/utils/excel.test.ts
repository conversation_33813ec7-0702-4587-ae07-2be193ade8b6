import { describe, it, expect, vi } from 'vitest'
import { exportToExcel } from '../../src/utils/excel'

// Mock XLSX
vi.mock('xlsx', () => ({
  utils: {
    json_to_sheet: vi.fn(() => ({})),
    book_new: vi.fn(() => ({})),
    book_append_sheet: vi.fn(),
  },
  writeFile: vi.fn(),
}))

describe('excel utils', () => {
  it('exportToExcel should work with basic data', () => {
    const data = [
      { name: '<PERSON>', age: 30 },
      { name: '<PERSON>', age: 25 }
    ]
    
    const columns = [
      { key: 'name', title: 'Name' },
      { key: 'age', title: 'Age' }
    ]

    // Should not throw error
    expect(() => {
      exportToExcel(data, columns, 'test')
    }).not.toThrow()
  })

  it('exportToExcel should handle empty data', () => {
    const data: any[] = []
    const columns = [
      { key: 'name', title: 'Name' }
    ]

    expect(() => {
      exportToExcel(data, columns, 'empty')
    }).not.toThrow()
  })
})
