import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { GTable } from '../../src'

describe('GTable', () => {
  const mockColumnDefs = [
    { field: 'name', headerName: 'Name' },
    { field: 'age', headerName: 'Age' }
  ]

  const mockRowData = [
    { name: '<PERSON>', age: 30 },
    { name: '<PERSON>', age: 25 }
  ]

  it('renders properly with props', () => {
    const wrapper = mount(GTable, {
      props: {
        columnDefs: mockColumnDefs,
        rowData: mockRowData
      },
      global: {
        stubs: {
          'ag-grid-vue': {
            template: '<div class="ag-grid-mock">AG Grid Mock</div>',
            props: ['columnDefs', 'rowData']
          }
        }
      }
    })
    
    expect(wrapper.find('.ag-grid-mock').exists()).toBe(true)
  })

  it('passes props to ag-grid-vue', () => {
    const wrapper = mount(GTable, {
      props: {
        columnDefs: mockColumnDefs,
        rowData: mockRowData,
        defaultColDef: { sortable: true }
      },
      global: {
        stubs: {
          'ag-grid-vue': {
            template: '<div class="ag-grid-mock">AG Grid Mock</div>',
            props: ['columnDefs', 'rowData', 'defaultColDef']
          }
        }
      }
    })
    
    const agGridComponent = wrapper.findComponent({ name: 'ag-grid-vue' })
    expect(agGridComponent.props('columnDefs')).toEqual(mockColumnDefs)
    expect(agGridComponent.props('rowData')).toEqual(mockRowData)
  })
})
