<template>
  <div id="app">
    <h1>Galaxy Vue Table - Development Environment</h1>
    
    <div class="demo-section">
      <h2>Basic Table</h2>
      <div class="component-demo">
        <GTable
          :column-defs="columnDefs"
          :row-data="rowData"
          :default-col-def="defaultColDef"
          style="height: 400px;"
        />
      </div>
    </div>

    <div class="demo-section">
      <h2>Excel Export</h2>
      <div class="component-demo">
        <GExcelExport
          :data="rowData"
          :columns="excelColumns"
          filename="sample-data"
        >
          <button>Export to Excel</button>
        </GExcelExport>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { GTable, GExcelExport } from '../src'

const columnDefs = ref([
  { field: 'make', headerName: 'Make', sortable: true, filter: true },
  { field: 'model', headerName: 'Model', sortable: true, filter: true },
  { field: 'price', headerName: 'Price', sortable: true, filter: true }
])

const rowData = ref([
  { make: 'Toyota', model: 'Celica', price: 35000 },
  { make: 'Ford', model: 'Mondeo', price: 32000 },
  { make: 'Porsche', model: 'Boxster', price: 72000 }
])

const defaultColDef = ref({
  flex: 1,
  minWidth: 100,
  resizable: true
})

const excelColumns = ref([
  { key: 'make', title: 'Make' },
  { key: 'model', title: 'Model' },
  { key: 'price', title: 'Price' }
])
</script>

<style scoped>
#app {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.component-demo {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

button {
  padding: 10px 20px;
  background-color: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #337ecc;
}
</style>
