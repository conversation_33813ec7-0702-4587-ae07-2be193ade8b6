{"name": "@galaxy-vue/shared", "version": "0.1.0", "description": "Galaxy Vue Shared - Common utilities, types, and constants", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "files": ["dist"], "scripts": {"build": "vue-tsc && vite build", "lint": "eslint src --ext .js,.ts --fix", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "keywords": ["vue", "vue3", "shared", "utilities", "types", "galaxy-vue"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-username/galaxy-vue.git", "directory": "packages/shared"}, "devDependencies": {"@types/node": "^22.15.31", "typescript": "^5.7.3", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.0", "vitest": "^3.1.1"}}