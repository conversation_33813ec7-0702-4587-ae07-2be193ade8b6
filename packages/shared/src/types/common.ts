/**
 * Common types used across Galaxy Vue components
 */

export type Size = 'small' | 'default' | 'large'

export type ButtonType = 'default' | 'primary' | 'success' | 'warning' | 'info' | 'danger'

export type ComponentStatus = 'loading' | 'success' | 'error' | 'warning'

export interface BaseComponentProps {
  id?: string
  class?: string | string[] | Record<string, boolean>
  style?: string | Record<string, any>
  disabled?: boolean
}

export interface TableColumn {
  key: string
  title: string
  width?: number
  sortable?: boolean
  filterable?: boolean
  align?: 'left' | 'center' | 'right'
}

export interface ExcelExportOptions {
  filename?: string
  sheetName?: string
  includeHeaders?: boolean
}

export interface PaginationConfig {
  page: number
  pageSize: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  pageSizes?: number[]
}
