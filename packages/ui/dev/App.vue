<template>
  <div id="app">
    <h1>Galaxy Vue UI - Development Environment</h1>
    
    <div class="demo-section">
      <h2>Button Components</h2>
      <div class="component-demo">
        <GButton>Default But<PERSON></GButton>
        <GButton type="primary">Primary Button</GButton>
        <GButton type="success">Success Button</GButton>
        <GButton type="warning">Warning Button</GButton>
        <GButton type="danger">Danger Button</GButton>
      </div>
    </div>

    <div class="demo-section">
      <h2>Card Components</h2>
      <div class="component-demo">
        <GCard title="Sample Card" style="width: 300px;">
          <p>This is a sample card content for testing purposes.</p>
          <template #footer>
            <GButton type="primary">Action</GButton>
          </template>
        </GCard>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { GButton, GCard } from '../src'
</script>

<style scoped>
#app {
  padding: 20px;
  font-family: Arial, sans-serif;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.component-demo {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.component-demo > * {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
