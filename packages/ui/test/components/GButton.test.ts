import { describe, it, expect } from 'vitest'
import { mount } from '@vue/test-utils'
import { GButton } from '../../src'

describe('GButton', () => {
  it('renders properly', () => {
    const wrapper = mount(GButton, {
      props: { type: 'primary' },
      slots: { default: 'Test Button' }
    })
    
    expect(wrapper.text()).toContain('Test Button')
    expect(wrapper.classes()).toContain('el-button--primary')
  })

  it('emits click event', async () => {
    const wrapper = mount(GButton, {
      slots: { default: 'Click me' }
    })
    
    await wrapper.trigger('click')
    expect(wrapper.emitted()).toHaveProperty('click')
  })

  it('can be disabled', () => {
    const wrapper = mount(GButton, {
      props: { disabled: true },
      slots: { default: 'Disabled <PERSON><PERSON>' }
    })
    
    expect(wrapper.attributes('disabled')).toBeDefined()
  })
})
