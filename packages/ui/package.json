{"name": "@galaxy-vue/ui", "version": "0.1.0", "description": "Galaxy Vue UI - A Vue 3 component library based on Element Plus", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./style": "./dist/style.css"}, "files": ["dist"], "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext .vue,.js,.ts,.jsx,.tsx --fix", "test": "vitest", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "keywords": ["vue", "vue3", "component", "ui", "element-plus", "galaxy-vue"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/your-username/galaxy-vue.git", "directory": "packages/ui"}, "peerDependencies": {"vue": "^3.5.0"}, "dependencies": {"element-plus": "^2.8.8"}, "devDependencies": {"@storybook/addon-docs": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/test": "^8.6.14", "@storybook/vue3": "^8.6.14", "@storybook/vue3-vite": "^8.6.14", "@types/node": "^22.15.31", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "sass": "^1.87.0", "storybook": "^8.6.14", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.28.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.5.0", "vitest": "^3.1.1", "vue": "^3.5.13", "vue-tsc": "^2.2.10"}}