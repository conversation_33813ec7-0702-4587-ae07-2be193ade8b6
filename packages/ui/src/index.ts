import type { App } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'

// 导入自定义组件
import GButton from './components/GButton/index.vue'
import GCard from './components/GCard/index.vue'

// 组件列表
const components = [
  GButton,
  GCard
]

// 定义 install 方法
const install = (app: App): void => {
  // 安装 Element Plus
  app.use(ElementPlus)

  // 注册自定义组件
  components.forEach(component => {
    const name = component.name || component.__name || 'UnknownComponent'
    app.component(name, component)
  })
}

// 导出组件库
export {
  // 组件
  GButton,
  GCard,
  // 安装方法
  install
}

// 默认导出
export default {
  install
}
