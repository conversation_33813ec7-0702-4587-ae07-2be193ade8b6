import type { Meta, StoryObj } from '@storybook/vue3'
import GCard from '../components/GCard/index.vue'
import GButton from '../components/GButton/index.vue'

const meta = {
  title: 'Galaxy Vue UI/GCard',
  component: GCard,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    header: {
      control: { type: 'text' }
    },
    shadow: {
      control: { type: 'select' },
      options: ['always', 'hover', 'never']
    }
  },
} satisfies Meta<typeof GCard>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    header: 'Card Title',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args }
    },
    template: `
      <GCard v-bind="args" style="width: 300px;">
        <p>This is the card content. You can put any content here.</p>
        <p>Cards are useful for displaying related information in a structured way.</p>
      </GCard>
    `,
  }),
}

export const WithCustomHeader: Story = {
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args }
    },
    template: `
      <GCard style="width: 300px;">
        <template #header>
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <span style="font-weight: bold; color: #409eff;">Custom Header</span>
            <span style="font-size: 12px; color: #999;">2024-01-16</span>
          </div>
        </template>
        <p>This card uses a custom header slot with additional styling and content.</p>
      </GCard>
    `,
  }),
}

export const WithFooter: Story = {
  args: {
    header: 'Card with Footer',
  },
  render: (args) => ({
    components: { GCard, GButton },
    setup() {
      return { args }
    },
    template: `
      <GCard v-bind="args" style="width: 300px;">
        <p>This card has a footer with action buttons.</p>
        <p>The footer is separated from the content with a border.</p>
        <template #footer>
          <div style="display: flex; gap: 8px; justify-content: flex-end;">
            <GButton size="small">Cancel</GButton>
            <GButton type="primary" size="small">Confirm</GButton>
          </div>
        </template>
      </GCard>
    `,
  }),
}

export const HoverShadow: Story = {
  args: {
    header: 'Hover Shadow Card',
    shadow: 'hover',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args }
    },
    template: `
      <GCard v-bind="args" style="width: 300px;">
        <p>This card only shows shadow on hover.</p>
        <p>Hover over this card to see the shadow effect.</p>
      </GCard>
    `,
  }),
}

export const NoShadow: Story = {
  args: {
    header: 'No Shadow Card',
    shadow: 'never',
  },
  render: (args) => ({
    components: { GCard },
    setup() {
      return { args }
    },
    template: `
      <GCard v-bind="args" style="width: 300px;">
        <p>This card never shows a shadow.</p>
        <p>It has a clean, flat appearance.</p>
      </GCard>
    `,
  }),
}
