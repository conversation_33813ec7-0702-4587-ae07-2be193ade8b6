import type { Meta, StoryObj } from '@storybook/vue3'
import { fn } from '@storybook/test'
import GButton from '../components/GButton/index.vue'

const meta = {
  title: 'Galaxy Vue UI/GButton',
  component: GButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: { type: 'select' },
      options: ['primary', 'success', 'warning', 'danger', 'info', 'text', '']
    },
    size: {
      control: { type: 'select' },
      options: ['large', 'default', 'small']
    },
    disabled: {
      control: { type: 'boolean' }
    },
    loading: {
      control: { type: 'boolean' }
    },
    round: {
      control: { type: 'boolean' }
    },
    circle: {
      control: { type: 'boolean' }
    },
    plain: {
      control: { type: 'boolean' }
    }
  },
  args: { onClick: fn() },
} satisfies Meta<typeof GButton>

export default meta
type Story = StoryObj<typeof meta>

export const Primary: Story = {
  args: {
    type: 'primary',
    default: 'Primary Button',
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">Primary Button</GButton>',
  }),
}

export const Success: Story = {
  args: {
    type: 'success',
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">Success Button</GButton>',
  }),
}

export const Warning: Story = {
  args: {
    type: 'warning',
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">Warning Button</GButton>',
  }),
}

export const Danger: Story = {
  args: {
    type: 'danger',
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">Danger Button</GButton>',
  }),
}

export const Loading: Story = {
  args: {
    loading: true,
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">Loading Button</GButton>',
  }),
}

export const Disabled: Story = {
  args: {
    disabled: true,
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">Disabled Button</GButton>',
  }),
}

export const Round: Story = {
  args: {
    round: true,
    type: 'primary',
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">Round Button</GButton>',
  }),
}

export const Circle: Story = {
  args: {
    circle: true,
    type: 'primary',
  },
  render: (args) => ({
    components: { GButton },
    setup() {
      return { args }
    },
    template: '<GButton v-bind="args">+</GButton>',
  }),
}
