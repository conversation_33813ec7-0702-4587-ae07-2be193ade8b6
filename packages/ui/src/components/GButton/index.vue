<template>
  <el-button
    :type="type"
    :size="size"
    :disabled="disabled"
    :loading="loading"
    :icon="icon"
    :round="round"
    :circle="circle"
    :plain="plain"
    :text="text"
    :bg="bg"
    :link="link"
    :color="color"
    :dark="dark"
    :auto-insert-space="autoInsertSpace"
    @click="handleClick"
  >
    <slot />
  </el-button>
</template>

<script setup lang="ts">
import { ElButton } from 'element-plus'
import type { Component } from 'vue'

defineOptions({
  name: 'GButton'
})

interface Props {
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info' | 'text' | ''
  size?: 'large' | 'default' | 'small'
  disabled?: boolean
  loading?: boolean
  icon?: string | Component
  round?: boolean
  circle?: boolean
  plain?: boolean
  text?: boolean
  bg?: boolean
  link?: boolean
  color?: string
  dark?: boolean
  autoInsertSpace?: boolean
}

withDefaults(defineProps<Props>(), {
  type: '',
  size: 'default',
  disabled: false,
  loading: false,
  round: false,
  circle: false,
  plain: false,
  text: false,
  bg: false,
  link: false,
  dark: false,
  autoInsertSpace: false
})

const emit = defineEmits<{
  click: [event: MouseEvent]
}>()

const handleClick = (event: MouseEvent) => {
  emit('click', event)
}
</script>

<style scoped>
/* 可以在这里添加自定义样式 */
</style>
