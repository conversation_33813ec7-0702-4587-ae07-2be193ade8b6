<template>
  <el-card
    :header="header"
    :body-style="bodyStyle"
    :shadow="shadow"
    class="g-card"
  >
    <template #header v-if="$slots.header">
      <slot name="header" />
    </template>

    <slot />

    <template #footer v-if="$slots.footer">
      <div class="g-card__footer">
        <slot name="footer" />
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
import { ElCard } from 'element-plus'
import type { CSSProperties } from 'vue'

defineOptions({
  name: 'GCard'
})

interface Props {
  header?: string
  bodyStyle?: CSSProperties
  shadow?: 'always' | 'hover' | 'never'
}

withDefaults(defineProps<Props>(), {
  shadow: 'always'
})
</script>

<style scoped>
.g-card {
  margin-bottom: 20px;
}

.g-card__footer {
  padding-top: 12px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
