<template>
  <div id="app">
    <div class="container">
      <h1>Galaxy Vue UI Components</h1>
      
      <section class="demo-section">
        <h2>GButton 组件示例</h2>
        <div class="button-group">
          <GButton type="primary">Primary Button</GButton>
          <GButton type="success">Success Button</GButton>
          <GButton type="warning">Warning Button</GButton>
          <GButton type="danger">Danger Button</GButton>
          <GButton type="info">Info Button</GButton>
        </div>
        
        <div class="button-group">
          <GButton plain>Plain Button</GButton>
          <GButton round>Round Button</GButton>
          <GButton circle>C</GButton>
          <GButton loading>Loading Button</GButton>
        </div>
      </section>

      <section class="demo-section">
        <h2>GCard 组件示例</h2>
        <div class="card-group">
          <GCard header="Card Title">
            <p>这是一个基于 Element Plus 的卡片组件示例。</p>
            <p>支持自定义头部、内容和底部。</p>
            <template #footer>
              <GButton type="primary" size="small">操作按钮</GButton>
            </template>
          </GCard>
          
          <GCard shadow="hover">
            <template #header>
              <span style="font-weight: bold;">自定义头部</span>
            </template>
            <p>这是另一个卡片示例，使用了自定义头部插槽。</p>
          </GCard>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件已经通过全局注册可以直接使用
</script>

<style scoped>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-section {
  margin-bottom: 40px;
}

.demo-section h2 {
  margin-bottom: 20px;
  color: #409eff;
}

.button-group {
  margin-bottom: 20px;
}

.button-group .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.card-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}
</style>
