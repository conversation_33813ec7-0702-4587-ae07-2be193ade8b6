{"name": "@galaxy-vue/root", "type": "module", "private": true, "description": "Galaxy Vue Component Library - A modern Vue 3 component library based on Element Plus", "repository": {"type": "git", "url": "git+https://github.com/your-username/galaxy-vue.git"}, "scripts": {"dev": "pnpm --filter @galaxy-vue/ui dev", "build": "lerna run build --stream", "build:ui": "pnpm --filter @galaxy-vue/ui build", "build:table": "pnpm --filter @galaxy-vue/table build", "lint": "lerna run lint --parallel --stream", "lint:fix": "lerna run lint:fix --parallel", "test": "lerna run test --parallel --stream", "clean": "lerna clean", "version": "lerna version", "publish": "lerna <PERSON>"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vue/compiler-sfc": "^3.5.13", "@vue/language-server": "^2.2.8", "eslint": "^8.57.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-n": "^16.6.2", "eslint-plugin-promise": "^6.6.0", "eslint-plugin-vue": "^10.0.0", "lerna": "^8.2.2", "rimraf": "^6.0.1", "typescript": "~5.8.3", "vue": "^3.5.13", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.10"}, "packageManager": "pnpm@10.12.1"}